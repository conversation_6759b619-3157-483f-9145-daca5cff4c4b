import React from 'react';

const NorwayGuidePage: React.FC = () => {
  return (
    <section className="min-h-screen w-full p-6">
      <div className="max-w-6xl mx-auto">
      <div className="flex flex-col md:flex-row gap-6">
        {/* Image Section */}
        <div className="md:w-1/2">
          <img 
            src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" 
            alt="Norway landscape with hammock" 
            className="w-full h-64 object-cover rounded-lg"
          />
        </div>
        
        {/* Content Section */}
        <div className="md:w-1/2 flex flex-col justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Norway Guide</h2>
            
            {/* Author Info */}
            <div className="flex items-center gap-2 mb-4">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-semibold">AS</span>
              </div>
              <div>
                <span className="text-sm text-gray-600">Guide by </span>
                <span className="text-sm font-semibold text-gray-800">Åsa Steinars</span>
                <span className="text-sm text-gray-500 ml-2">Norway</span>
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full ml-2">New</span>
              </div>
            </div>
            
            {/* Description */}
            <p className="text-gray-600 text-sm mb-4 leading-relaxed">
              Norway is my second home. I was born in Norway and I lived there until I was 7 years old. I 
              often come back and I love this country almost as much as Iceland. Last summer I spent 3 
              months on the road with my van exploring everything from the south tip up to Lofoten.
            </p>
            
            <p className="text-gray-600 text-sm mb-6 leading-relaxed">
              This guide is my best tips for Norway to make sure you get the most out of your trip. It is 
              focused around the fjords in the west and Lofoten in the north. In my opinion, it's the best 
              areas to explore in Norway.
            </p>
          </div>
          
          {/* Action Buttons */}
          <div className="flex gap-3">
            <button className="flex-1 py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              Preview
            </button>
            <button className="flex-1 py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              Get Access
            </button>
          </div>
          
          {/* Price Info */}
          <p className="text-xs text-gray-500 text-center mt-2">
            Pays for 100+ tips
          </p>
        </div>
      </div>
      </div>
    </section>
  );
};

export default NorwayGuidePage;
