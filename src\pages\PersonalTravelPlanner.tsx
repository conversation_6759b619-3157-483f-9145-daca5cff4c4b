import React, { useState } from 'react';

const PersonalTravelPlannerPage: React.FC = () => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  const handleSendMessage = () => {
    if (message.trim()) {
      setIsTyping(true);
      // Simulate AI response delay
      setTimeout(() => {
        setIsTyping(false);
      }, 2000);
      setMessage('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <section className="min-h-screen w-full p-6 bg-gray-50">
      <div className="max-w-6xl mx-auto bg-white rounded-lg shadow-lg p-8">
      <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">Your Personal Travel Planner</h2>
      
      <div className="grid md:grid-cols-2 gap-6">
        {/* Left Side - Ask me Anything */}
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Ask me Anything</h3>
          <p className="text-sm text-gray-600 mb-4">
            Rexby is trained on Åsa Steinars' local knowledge, enabling it to 
            answer questions just like Åsa Steinars, but faster.
          </p>
          
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <p className="text-sm font-medium text-gray-700 mb-2">Preview</p>
            <div className="space-y-3">
              <div className="bg-blue-100 text-blue-800 p-3 rounded-lg text-sm">
                What is the best season to visit?
              </div>
              {isTyping && (
                <div className="bg-gray-200 p-3 rounded-lg text-sm">
                  <span className="text-gray-600">Thinking...</span>
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* Right Side - Chat Interface */}
        <div>
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-semibold">AS</span>
            </div>
            <div>
              <p className="font-semibold text-gray-800">Åsa Steinars</p>
              <p className="text-sm text-gray-500">Expert on Norway</p>
            </div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <p className="text-sm text-gray-600 mb-3">
              Hi there, I am Åsa Steinars AI. I have been trained to answer travel questions just like Åsa 
              Steinars would do in person, but faster.
            </p>
            <p className="text-sm font-medium text-gray-700">What is the best season to visit?</p>
          </div>
          
          {/* Message Input */}
          <div className="flex gap-2">
            <input
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Message..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <button
              onClick={handleSendMessage}
              disabled={!message.trim()}
              className="w-10 h-10 bg-gray-800 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            </button>
          </div>
        </div>
      </div>
      </div>
    </section>
  );
};

export default PersonalTravelPlannerPage;
