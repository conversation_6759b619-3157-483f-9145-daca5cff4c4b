import React, { useState } from 'react';

interface LocalSpot {
  id: string;
  title: string;
  category: string;
  image: string;
  description: string;
  location: string;
}

const AccessMyLocalSecretsPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  
  const localSpots: LocalSpot[] = [
    {
      id: '1',
      title: 'One of my favourite spots',
      category: 'Sightseeing',
      image: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=400&h=300&fit=crop',
      description: 'A breathtaking viewpoint with panoramic views',
      location: 'Geiranger'
    },
    {
      id: '2',
      title: 'Swing with amazing views',
      category: 'Sightseeing',
      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop',
      description: 'Experience the thrill of swinging over the fjord',
      location: 'Lofoten'
    },
    {
      id: '3',
      title: 'Beautiful view point',
      category: 'Hike',
      image: 'https://images.unsplash.com/photo-1531366936337-7c912a4589a7?w=400&h=300&fit=crop',
      description: 'Perfect spot for sunrise and sunset photography',
      location: 'Preikestolen'
    },
    {
      id: '4',
      title: 'Hidden waterfall',
      category: 'Nature',
      image: 'https://images.unsplash.com/photo-1439066615861-d1af74d74000?w=400&h=300&fit=crop',
      description: 'A secluded waterfall away from tourist crowds',
      location: 'Hardanger'
    },
    {
      id: '5',
      title: 'Cozy mountain cabin',
      category: 'Accommodation',
      image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400&h=300&fit=crop',
      description: 'Traditional Norwegian cabin with modern amenities',
      location: 'Jotunheimen'
    },
    {
      id: '6',
      title: 'Local fishing spot',
      category: 'Activity',
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop',
      description: 'Best kept secret for catching Arctic char',
      location: 'Lofoten'
    }
  ];

  const categories = ['all', 'Sightseeing', 'Hike', 'Nature', 'Accommodation', 'Activity'];
  
  const filteredSpots = selectedCategory === 'all' 
    ? localSpots 
    : localSpots.filter(spot => spot.category === selectedCategory);

  return (
    <section className="min-h-screen w-full p-6 bg-gray-50">
      <div className="max-w-7xl mx-auto bg-white rounded-lg shadow-lg p-8">
      <div className="flex items-center gap-2 mb-6">
        <button className="text-gray-600 hover:text-gray-800">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <h2 className="text-2xl font-bold text-gray-800">Access My Local Secrets</h2>
      </div>
      
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">
          {filteredSpots.length} things to do
        </h3>
        <p className="text-gray-600 text-sm">
          Get a curated list of all the best things to do with exact location, detailed info 
          and inspiring content.
        </p>
        
        <div className="mt-4">
          <p className="text-sm font-medium text-gray-700 mb-2">Preview</p>
        </div>
      </div>
      
      {/* Category Filter */}
      <div className="flex flex-wrap gap-2 mb-6">
        {categories.map((category) => (
          <button
            key={category}
            onClick={() => setSelectedCategory(category)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              selectedCategory === category
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {category === 'all' ? 'All' : category}
          </button>
        ))}
      </div>
      
      {/* Spots Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredSpots.map((spot) => (
          <div key={spot.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <div className="relative">
              <img
                src={spot.image}
                alt={spot.title}
                className="w-full h-48 object-cover"
              />
              <div className="absolute top-3 left-3">
                <span className="bg-white bg-opacity-90 text-gray-800 px-2 py-1 rounded-full text-xs font-medium">
                  {spot.category}
                </span>
              </div>
              <div className="absolute top-3 right-3">
                <button className="bg-white bg-opacity-90 p-1 rounded-full hover:bg-opacity-100 transition-colors">
                  <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                </button>
              </div>
            </div>
            
            <div className="p-4">
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-semibold text-gray-800 text-sm leading-tight">
                  {spot.title}
                </h4>
                <div className="flex items-center gap-1 ml-2">
                  <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <span className="text-xs text-gray-500">{spot.location}</span>
                </div>
              </div>
              
              <p className="text-gray-600 text-xs mb-3 line-clamp-2">
                {spot.description}
              </p>
              
              <div className="flex items-center justify-between">
                <button className="text-blue-600 hover:text-blue-700 text-xs font-medium">
                  Learn More
                </button>
                <div className="flex gap-1">
                  <button className="p-1 text-gray-400 hover:text-gray-600">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {filteredSpots.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No spots found in this category.</p>
        </div>
      )}
      </div>
    </section>
  );
};

export default AccessMyLocalSecretsPage;
