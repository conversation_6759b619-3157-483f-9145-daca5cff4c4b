import React, { useState } from 'react';

const InteractiveMapPage: React.FC = () => {
  const [selectedLocation, setSelectedLocation] = useState<string | null>(null);

  const locations = [
    { id: 'oslo', name: 'Oslo', x: 45, y: 70, description: 'Capital city with rich history' },
    { id: 'bergen', name: 'Bergen', x: 25, y: 60, description: 'Gateway to the fjords' },
    { id: 'trondheim', name: 'Trondheim', x: 40, y: 45, description: 'Historic city with colorful buildings' },
    { id: 'lofoten', name: 'Lofoten', x: 35, y: 20, description: 'Dramatic peaks and fishing villages' },
    { id: 'nordkapp', name: 'North Cape', x: 50, y: 5, description: 'Northernmost point of Europe' },
  ];

  return (
    <section className="min-h-screen w-full p-6">
      <div className="max-w-6xl mx-auto">
      <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">Interactive Map</h2>
      
      <div className="grid md:grid-cols-2 gap-6">
        {/* Map Section */}
        <div className="relative">
          <div className="bg-gradient-to-b from-blue-100 to-green-100 rounded-lg p-4 h-80 relative overflow-hidden">
            {/* Simplified Norway outline */}
            <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100">
              <path
                d="M30 90 L25 85 L20 75 L15 65 L20 55 L25 45 L30 35 L35 25 L40 15 L45 10 L50 5 L45 15 L50 25 L45 35 L40 45 L45 55 L50 65 L45 75 L40 85 L35 90 Z"
                fill="rgba(34, 197, 94, 0.3)"
                stroke="rgba(34, 197, 94, 0.6)"
                strokeWidth="1"
              />
            </svg>
            
            {/* Location markers */}
            {locations.map((location) => (
              <button
                key={location.id}
                className={`absolute w-3 h-3 rounded-full transform -translate-x-1/2 -translate-y-1/2 transition-all ${
                  selectedLocation === location.id
                    ? 'bg-red-500 scale-150 z-10'
                    : 'bg-blue-500 hover:bg-blue-600 hover:scale-125'
                }`}
                style={{ left: `${location.x}%`, top: `${location.y}%` }}
                onClick={() => setSelectedLocation(location.id)}
                title={location.name}
              />
            ))}
          </div>
          
          <p className="text-xs text-gray-500 mt-2 text-center">
            Click on the markers to explore different locations
          </p>
        </div>
        
        {/* Location Details */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-800">Explore Norway</h3>
          
          {selectedLocation ? (
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-2">
                {locations.find(loc => loc.id === selectedLocation)?.name}
              </h4>
              <p className="text-blue-700 text-sm">
                {locations.find(loc => loc.id === selectedLocation)?.description}
              </p>
              <button className="mt-3 px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors">
                Learn More
              </button>
            </div>
          ) : (
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-gray-600 text-sm">
                Select a location on the map to learn more about it.
              </p>
            </div>
          )}
          
          {/* Quick Links */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-700">Popular Destinations:</h4>
            <div className="grid grid-cols-2 gap-2">
              {locations.slice(0, 4).map((location) => (
                <button
                  key={location.id}
                  onClick={() => setSelectedLocation(location.id)}
                  className="text-left p-2 text-sm text-blue-600 hover:bg-blue-50 rounded transition-colors"
                >
                  {location.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
      </div>
    </section>
  );
};

export default InteractiveMapPage;
