import React, { useState } from 'react';

interface ItineraryItem {
  id: string;
  name: string;
  image: string;
  type: 'activity' | 'place' | 'accommodation';
  day?: number;
}

const BuildItineraryPage: React.FC = () => {
  const [selectedDay, setSelectedDay] = useState(1);
  
  const availableSpots: ItineraryItem[] = [
    { id: '1', name: 'Hot Spring', image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=100&h=100&fit=crop', type: 'activity' },
    { id: '2', name: '<PERSON><PERSON><PERSON>', image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=100&h=100&fit=crop', type: 'place' },
    { id: '3', name: 'Waterfall', image: 'https://images.unsplash.com/photo-1439066615861-d1af74d74000?w=100&h=100&fit=crop', type: 'place' },
    { id: '4', name: 'Flydalsjuvet', image: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=100&h=100&fit=crop', type: 'place' },
    { id: '5', name: 'Lofty', image: 'https://images.unsplash.com/photo-1531366936337-7c912a4589a7?w=100&h=100&fit=crop', type: 'accommodation' },
  ];

  const [itinerary, setItinerary] = useState<{ [key: number]: ItineraryItem[] }>({
    1: [],
    2: [],
    3: [],
  });

  const addToItinerary = (item: ItineraryItem) => {
    setItinerary(prev => ({
      ...prev,
      [selectedDay]: [...prev[selectedDay], { ...item, day: selectedDay }]
    }));
  };

  const removeFromItinerary = (dayNum: number, itemId: string) => {
    setItinerary(prev => ({
      ...prev,
      [dayNum]: prev[dayNum].filter(item => item.id !== itemId)
    }));
  };

  const days = [1, 2, 3];

  return (
    <section className="min-h-screen w-full p-6">
      <div className="max-w-7xl mx-auto">
      <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">Build Itinerary with my Spots</h2>
      
      <p className="text-gray-600 text-center mb-6">
        Start with a pre-made itinerary as a template, or build you own from scratch by using all the listed things to do and adding your own.
      </p>
      
      <div className="mb-6">
        <p className="text-sm font-medium text-gray-700 mb-2">Preview</p>
      </div>
      
      <div className="grid lg:grid-cols-4 gap-6">
        {/* Available Spots */}
        <div className="lg:col-span-1">
          <h3 className="font-semibold text-gray-800 mb-4">Available Spots</h3>
          <div className="space-y-3">
            {availableSpots.map((spot) => (
              <div
                key={spot.id}
                className="flex items-center gap-3 p-2 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => addToItinerary(spot)}
              >
                <img
                  src={spot.image}
                  alt={spot.name}
                  className="w-12 h-12 object-cover rounded"
                />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-800">{spot.name}</p>
                  <p className="text-xs text-gray-500 capitalize">{spot.type}</p>
                </div>
                <button className="text-blue-600 hover:text-blue-700">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </button>
              </div>
            ))}
          </div>
        </div>
        
        {/* Itinerary Days */}
        <div className="lg:col-span-3">
          <div className="flex gap-4 mb-4">
            {days.map((day) => (
              <button
                key={day}
                onClick={() => setSelectedDay(day)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  selectedDay === day
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                Day {day}
              </button>
            ))}
          </div>
          
          <div className="grid md:grid-cols-3 gap-4">
            {days.map((day) => (
              <div key={day} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-gray-800">Day {day}</h4>
                  <span className="text-xs text-gray-500">
                    {itinerary[day].length} items
                  </span>
                </div>
                
                <div className="space-y-2 min-h-[200px]">
                  {itinerary[day].length === 0 ? (
                    <div className="flex items-center justify-center h-32 text-gray-400 text-sm">
                      Drop items here
                    </div>
                  ) : (
                    itinerary[day].map((item, index) => (
                      <div key={`${item.id}-${index}`} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-8 h-8 object-cover rounded"
                        />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-800">{item.name}</p>
                        </div>
                        <button
                          onClick={() => removeFromItinerary(day, item.id)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    ))
                  )}
                </div>
                
                {/* Action buttons for each day */}
                <div className="mt-3 space-y-2">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <span>📍</span>
                    <span>Thing to do</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <span>📝</span>
                    <span>Text</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <span>📌</span>
                    <span>New pin</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      </div>
    </section>
  );
};

export default BuildItineraryPage;
