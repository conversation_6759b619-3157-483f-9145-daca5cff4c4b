import React from 'react';
import { Routes, Route, Link, useLocation } from 'react-router-dom';
import NorwayGuidePage from './NorwayGuide';
import PersonalTravelPlannerPage from './PersonalTravelPlanner';
import InteractiveMapPage from './InteractiveMap';
import BuildItineraryPage from './BuildItinerary';
import AccessMyLocalSecretsPage from './AccessMyLocalSecrets';

const Home: React.FC = () => {
  const location = useLocation();

  const sections = [
    { id: 'guide', name: 'Norway Guide', icon: '📖', path: '/guide' },
    { id: 'planner', name: 'Travel Planner', icon: '🤖', path: '/planner' },
    { id: 'map', name: 'Interactive Map', icon: '🗺️', path: '/map' },
    { id: 'itinerary', name: 'Build Itinerary', icon: '📅', path: '/itinerary' },
    { id: 'secrets', name: 'Local Secrets', icon: '🔍', path: '/secrets' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">R</span>
              </div>
              <span className="text-xl font-bold text-gray-800">Rexby</span>
            </div>
            <div className="flex items-center gap-4">
              <button className="text-gray-600 hover:text-gray-800 text-sm">Log In</button>
              <button className="text-gray-600 hover:text-gray-800">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                </svg>
              </button>
              <button className="text-gray-600 hover:text-gray-800">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white border-b sticky top-16 z-40">
        <div className="max-w-6xl mx-auto px-6 overflow-x-auto">
          <div className="flex space-x-6 py-2">
            {sections.map((section) => (
              <Link
                key={section.id}
                to={section.path}
                className={`flex items-center gap-2 py-2 px-3 whitespace-nowrap transition-colors ${
                  location.pathname === section.path
                    ? 'text-blue-600 border-b-2 border-blue-600'
                    : 'text-gray-600 hover:text-blue-600 hover:underline'
                }`}
              >
                <span>{section.icon}</span>
                <span className="font-medium">{section.name}</span>
              </Link>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content - Routed pages */}
      <main className="py-16">
        <Routes>
          <Route path="/guide" element={<NorwayGuidePage />} />
          <Route path="/planner" element={<PersonalTravelPlannerPage />} />
          <Route path="/map" element={<InteractiveMapPage />} />
          <Route path="/itinerary" element={<BuildItineraryPage />} />
          <Route path="/secrets" element={<AccessMyLocalSecretsPage />} />
          <Route path="/" element={<NorwayGuidePage />} />
        </Routes>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-16">
        <div className="max-w-6xl mx-auto px-6 py-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
                  <span className="text-white font-bold text-xs">R</span>
                </div>
                <span className="font-bold text-gray-800">Rexby</span>
              </div>
              <p className="text-gray-600 text-sm">
                Your personal travel companion for discovering Norway's hidden gems.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-gray-800 mb-3">Explore</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-gray-800">Destinations</a></li>
                <li><a href="#" className="hover:text-gray-800">Activities</a></li>
                <li><a href="#" className="hover:text-gray-800">Local Guides</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-gray-800 mb-3">Support</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-gray-800">Help Center</a></li>
                <li><a href="#" className="hover:text-gray-800">Contact Us</a></li>
                <li><a href="#" className="hover:text-gray-800">FAQ</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-gray-800 mb-3">Connect</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li><a href="#" className="hover:text-gray-800">Newsletter</a></li>
                <li><a href="#" className="hover:text-gray-800">Social Media</a></li>
                <li><a href="#" className="hover:text-gray-800">Community</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t mt-8 pt-6 text-center text-sm text-gray-500">
            <p>&copy; 2024 Rexby. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Home;
